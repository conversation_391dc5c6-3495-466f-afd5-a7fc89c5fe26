//
//  APIManager.swift
//  TinyVoice
//
//  Created by <PERSON> on 8/9/25.
//

import Foundation
import Combine
import AVFoundation



class APIManager: ObservableObject {
    private var playbackPlayer: AVAudioPlayer?

    @Published var userTranscript = ""
    @Published var aiTranscript = ""
    @Published var isProcessing = false
    @Published var errorMessage = ""

    private let apiURL = URL(string: "http://*************:8000/api/chat")!
    private var cancellables = Set<AnyCancellable>()

    struct APIResponse: Codable {
        let audio_data: String
        let transcript: String
        let user_transcript: String
    }

    func sendAudio(_ audioData: Data, completion: @escaping (Result<APIResponse, Error>) -> Void) {
        isProcessing = true
        errorMessage = ""

        // Create multipart form data
        let boundary = UUID().uuidString
        var request = URLRequest(url: apiURL)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        var body = Data()

        // Add audio file
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"audio_file\"; filename=\"recording.wav\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: audio/wav\r\n\r\n".data(using: .utf8)!)
        body.append(audioData)
        body.append("\r\n--\(boundary)--\r\n".data(using: .utf8)!)

        request.httpBody = body

        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isProcessing = false

                if let error = error {
                    self?.errorMessage = "Network error: \(error.localizedDescription)"
                    completion(.failure(error))
                    return
                }

                guard let httpResponse = response as? HTTPURLResponse else {
                    let error = NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
                    self?.errorMessage = "Invalid response from server"
                    completion(.failure(error))
                    return
                }

                guard httpResponse.statusCode == 200 else {
                    let error = NSError(domain: "APIError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server error: \(httpResponse.statusCode)"])
                    self?.errorMessage = "Server error: \(httpResponse.statusCode)"
                    completion(.failure(error))
                    return
                }

                guard let data = data else {
                    let error = NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "No data received"])
                    self?.errorMessage = "No data received from server"
                    completion(.failure(error))
                    return
                }

                do {
                    let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                    self?.userTranscript = apiResponse.user_transcript
                    self?.aiTranscript = apiResponse.transcript
                    completion(.success(apiResponse))
                } catch {
                    self?.errorMessage = "Failed to parse response: \(error.localizedDescription)"
                    completion(.failure(error))
                }
            }
        }.resume()
    }

    func playAudioResponse(_ base64AudioData: String, completion: @escaping (Bool) -> Void) {
        guard let audioData = Data(base64Encoded: base64AudioData) else {
            DispatchQueue.main.async {
                self.errorMessage = "Invalid audio data received"
                completion(false)
            }
            return
        }

        do {
            // Detect audio format (mp3 vs wav) by magic bytes
            let ext: String
            if audioData.count >= 3, let id3 = String(data: audioData.prefix(3), encoding: .ascii), id3 == "ID3" {
                ext = "mp3"
            } else if audioData.count >= 2 && audioData[0] == 0xFF && (audioData[1] & 0xE0) == 0xE0 {
                // MPEG frame header
                ext = "mp3"
            } else if audioData.count >= 12, let hdr = String(data: audioData.prefix(12), encoding: .ascii), hdr.hasPrefix("RIFF") && hdr.contains("WAVE") {
                ext = "wav"
            } else {
                ext = "mp3" // default
            }

            // Create temporary file for audio playback
            let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("response_\(UUID().uuidString).\(ext)")
            try audioData.write(to: tempURL)

            // Use existing audio session (configured for playAndRecord elsewhere)
            // Avoid changing the audio session category here to prevent '!pri' errors during capture

            // Play audio using AVAudioPlayer
            self.playbackPlayer = try AVAudioPlayer(contentsOf: tempURL)
            self.playbackPlayer?.volume = 1.0
            self.playbackPlayer?.play()

            // Clean up temp file after playback
            DispatchQueue.main.asyncAfter(deadline: .now() + max((self.playbackPlayer?.duration ?? 1) + 1, 2)) {
                try? FileManager.default.removeItem(at: tempURL)
                completion(true)
            }

        } catch {
            print("Error playing audio response: \(error)")
            DispatchQueue.main.async {
                self.errorMessage = "Failed to play audio response: \(error.localizedDescription)"
                completion(false)
            }
        }
    }

    func clearTranscripts() {
        userTranscript = ""
        aiTranscript = ""
        errorMessage = ""
    }
}
