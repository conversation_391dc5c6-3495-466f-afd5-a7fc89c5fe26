//
//  AudioManager.swift
//  TinyVoice
//
//  Created by <PERSON> on 8/9/25.
//

import Foundation
import AVFoundation
import Combine

class AudioManager: NSObject, ObservableObject {
    @Published var isRecording = false
    @Published var isSending = false
    @Published var status = "Requesting microphone access..."

    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioBuffer: [AudioChunk] = []
    private var wakeWordAudioBuffer: [AudioChunk] = []

    private let bufferDuration: TimeInterval = 15.0 // 15 seconds
    private let sampleRate: Double = 44100
    private let maxSamples: Int

    // Audio session and playback
    private var audioPlayer: AVAudioPlayer?
    private var typingPlayer: AVAudioPlayer?
    private var isTypingSoundPlaying = false

    // Wake word detection state
    var isListeningAfterWakeWord = false
    private var silenceThreshold: Float = 0.01
    private var silenceTimeout: TimeInterval = 2.0
    private var silenceStartTime: Date?

    // API Manager reference
    weak var apiManager: APIManager?
    
    struct AudioChunk {
        let data: [Float]
        let timestamp: Date
    }
    
    override init() {
        self.maxSamples = Int(sampleRate * bufferDuration)
        super.init()
        setupAudioSession()
        setupNotificationObservers()
    }

    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWakeWordDetected),
            name: .wakeWordDetected,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleExecutionKeywordDetected),
            name: .executionKeywordDetected,
            object: nil
        )
    }

    @objc private func handleWakeWordDetected() {
        // Initialize wake word buffer with the ENTIRE main buffer
        wakeWordAudioBuffer = audioBuffer.map { AudioChunk(data: $0.data, timestamp: $0.timestamp) }
        isListeningAfterWakeWord = true
        silenceStartTime = nil
    }

    @objc private func handleExecutionKeywordDetected() {
        handleSilenceDetected()
    }
    
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            
            // Request microphone permission
            audioSession.requestRecordPermission { [weak self] granted in
                DispatchQueue.main.async {
                    if granted {
                        self?.status = "Microphone access granted"
                    } else {
                        self?.status = "Microphone access denied"
                    }
                }
            }
        } catch {
            DispatchQueue.main.async {
                self.status = "Audio session setup failed: \(error.localizedDescription)"
            }
        }
    }
    
    func startContinuousRecording() {
        guard !isRecording else { return }

        do {
            audioEngine = AVAudioEngine()
            guard let audioEngine = audioEngine else { return }

            inputNode = audioEngine.inputNode
            guard let inputNode = inputNode else { return }

            let recordingFormat = inputNode.outputFormat(forBus: 0)
            print("AudioManager - Input format: \(recordingFormat)")

            inputNode.installTap(onBus: 0, bufferSize: 4096, format: recordingFormat) { [weak self] buffer, _ in
                self?.processAudioBuffer(buffer)
            }

            try audioEngine.start()

            DispatchQueue.main.async {
                self.isRecording = true
                self.status = "Continuously recording... Click button to send last 15 seconds."
            }

        } catch {
            DispatchQueue.main.async {
                self.status = "Failed to start recording: \(error.localizedDescription)"
            }
        }
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else {
            print("AudioManager - No float channel data available")
            return
        }
        let frameLength = Int(buffer.frameLength)

        let audioData = Array(UnsafeBufferPointer(start: channelData, count: frameLength))
        let chunk = AudioChunk(data: audioData, timestamp: Date())

        DispatchQueue.main.async {
            self.addToBuffer(chunk)

            if self.isListeningAfterWakeWord {
                self.wakeWordAudioBuffer.append(chunk)
                self.checkForSilence(audioData)
            }
        }
    }
    
    private func addToBuffer(_ chunk: AudioChunk) {
        audioBuffer.append(chunk)
        
        // Remove chunks older than 15 seconds
        let cutoffTime = Date().addingTimeInterval(-bufferDuration)
        audioBuffer.removeAll { $0.timestamp < cutoffTime }
        
        // Limit total samples
        let totalSamples = audioBuffer.reduce(0) { $0 + $1.data.count }
        if totalSamples > maxSamples {
            while !audioBuffer.isEmpty && audioBuffer.reduce(0, { $0 + $1.data.count }) > maxSamples {
                audioBuffer.removeFirst()
            }
        }
    }
    
    private func checkForSilence(_ audioData: [Float]) {
        // Calculate RMS
        let rms = sqrt(audioData.map { $0 * $0 }.reduce(0, +) / Float(audioData.count))
        let currentTime = Date()
        
        if rms < silenceThreshold {
            if silenceStartTime == nil {
                silenceStartTime = currentTime
            } else if let silenceStart = silenceStartTime,
                      currentTime.timeIntervalSince(silenceStart) >= silenceTimeout {
                handleSilenceDetected()
            }
        } else {
            silenceStartTime = nil
        }
    }
    
    private func handleSilenceDetected() {
        isListeningAfterWakeWord = false
        silenceStartTime = nil
        
        DispatchQueue.main.async {
            self.processWakeWordAudio()
        }
    }
    
    func sendLastFifteenSeconds() {
        guard !isSending, !audioBuffer.isEmpty else { return }
        
        isSending = true
        status = "Preparing audio..."
        
        let combinedAudio = combineAudioChunks(audioBuffer)
        let wavData = createWAVData(from: combinedAudio)
        
        // Clear buffer after sending
        audioBuffer.removeAll()
        
        // Send to API
        sendToAPI(wavData)
    }
    
    private func processWakeWordAudio() {
        guard !wakeWordAudioBuffer.isEmpty else {
            status = "No audio captured after wake word"
            return
        }
        
        let combinedAudio = combineAudioChunks(wakeWordAudioBuffer)
        let wavData = createWAVData(from: combinedAudio)
        
        wakeWordAudioBuffer.removeAll()
        audioBuffer.removeAll()
        
        sendToAPI(wavData)
    }
    
    private func combineAudioChunks(_ chunks: [AudioChunk]) -> [Float] {
        return chunks.flatMap { $0.data }
    }
    
    private func createWAVData(from audioData: [Float]) -> Data {
        let sampleRate = Int32(self.sampleRate)
        let numChannels: Int16 = 1
        let bitsPerSample: Int16 = 16
        let byteRate = sampleRate * Int32(numChannels) * Int32(bitsPerSample) / 8
        let blockAlign = numChannels * bitsPerSample / 8
        let dataSize = Int32(audioData.count * 2) // 16-bit samples
        let fileSize = 36 + dataSize
        
        var wavData = Data()
        
        // RIFF header
        wavData.append("RIFF".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: fileSize.littleEndian) { Data($0) })
        wavData.append("WAVE".data(using: .ascii)!)
        
        // fmt chunk
        wavData.append("fmt ".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: Int32(16).littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: Int16(1).littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: numChannels.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: sampleRate.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: byteRate.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: blockAlign.littleEndian) { Data($0) })
        wavData.append(withUnsafeBytes(of: bitsPerSample.littleEndian) { Data($0) })
        
        // data chunk
        wavData.append("data".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: dataSize.littleEndian) { Data($0) })
        
        // Convert float samples to 16-bit PCM
        for sample in audioData {
            let clampedSample = max(-1.0, min(1.0, sample))
            let intSample = Int16(clampedSample < 0 ? clampedSample * 32768 : clampedSample * 32767)
            wavData.append(withUnsafeBytes(of: intSample.littleEndian) { Data($0) })
        }
        
        return wavData
    }
    
    private func sendToAPI(_ wavData: Data) {
        guard let apiManager = apiManager else {
            status = "API Manager not available"
            isSending = false
            return
        }

        startTypingSound()
        status = "Sending to AI..."

        apiManager.sendAudio(wavData) { [weak self] result in
            DispatchQueue.main.async {
                self?.stopTypingSound()
                self?.isSending = false

                switch result {
                case .success(let response):
                    self?.status = "Response received! Still recording..."

                    // Play the audio response
                    apiManager.playAudioResponse(response.audio_data) { success in
                        if !success {
                            print("Failed to play audio response")
                        }

                        // Reset session state after assistant finishes speaking
                        // This allows the next wake word detection to work
                        DispatchQueue.main.async {
                            self?.resetSessionAfterResponse()
                        }
                    }

                case .failure(let error):
                    self?.status = "Error: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func startTypingSound() {
        guard !isTypingSoundPlaying else { return }
        
        if let path = Bundle.main.path(forResource: "Typing", ofType: "wav") {
            do {
                typingPlayer = try AVAudioPlayer(contentsOf: URL(fileURLWithPath: path))
                typingPlayer?.numberOfLoops = -1 // Loop indefinitely
                typingPlayer?.volume = 0.3
                typingPlayer?.play()
                isTypingSoundPlaying = true
            } catch {
                print("Error playing typing sound: \(error)")
            }
        }
    }
    
    func stopTypingSound() {
        typingPlayer?.stop()
        typingPlayer = nil
        isTypingSoundPlaying = false
    }

    private func resetSessionAfterResponse() {
        // Reset the wake word listening state to allow next interaction
        isListeningAfterWakeWord = false
        silenceStartTime = nil

        // Clear any remaining wake word audio buffer
        wakeWordAudioBuffer.removeAll()

        // Update status to indicate ready for next wake word
        status = "Response complete! Still recording..."

        // Notify WebSocketManager to reset its session state
        NotificationCenter.default.post(name: .sessionReset, object: nil)

        print("Session reset after assistant response - ready for next wake word")
    }
    
    func cleanup() {
        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        audioEngine = nil
        inputNode = nil
        isRecording = false
        stopTypingSound()

        // Remove notification observers
        NotificationCenter.default.removeObserver(self)
    }
}
