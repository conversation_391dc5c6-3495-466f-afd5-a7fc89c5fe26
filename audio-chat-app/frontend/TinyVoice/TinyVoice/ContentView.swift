//
//  ContentView.swift
//  TinyVoice
//
//  Created by <PERSON> on 7/31/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var audioManager = AudioManager()
    @StateObject private var webSocketManager = WebSocketManager()
    @StateObject private var apiManager = APIManager()

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("Audio Chat with AI")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)

                // Controls
                HStack(spacing: 20) {
                    Button(action: {
                        audioManager.sendLastFifteenSeconds()
                    }) {
                        Text(audioManager.isRecording ? "Send Last 15 Seconds" : "Start Recording")
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                    .disabled(!audioManager.isRecording || audioManager.isSending)

                    Button(action: {
                        webSocketManager.toggleWakeWordListening()
                    }) {
                        Text(webSocketManager.isListening ? "Stop Listening" : "Start Listening")
                            .padding()
                            .background(webSocketManager.isListening ? Color.red : Color.green)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                }

                // Wake word indicator
                if webSocketManager.isListening {
                    HStack {
                        Circle()
                            .fill(webSocketManager.wakeWordStatus.contains("offline") ? Color.red : Color.green)
                            .frame(width: 8, height: 8)
                            .opacity(webSocketManager.wakeWordStatus.contains("offline") ? 1.0 : 0.5)
                            .animation(.easeInOut(duration: 1).repeatForever(), value: webSocketManager.isListening)

                        Text(webSocketManager.wakeWordStatus)
                            .font(.caption)
                    }
                    .padding()
                    .background(webSocketManager.wakeWordStatus.contains("detected") ? Color.green.opacity(0.2) : Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }

                // Status
                Text(audioManager.status)
                    .padding()
                    .background(audioManager.status.contains("Error") ? Color.red.opacity(0.1) : Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .multilineTextAlignment(.center)

                // Error message
                if !apiManager.errorMessage.isEmpty {
                    Text(apiManager.errorMessage)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }

                // Transcripts
                if !apiManager.userTranscript.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("You said:")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.blue)

                        Text("\"\(apiManager.userTranscript)\"")
                            .italic()
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                    }
                }

                if !apiManager.aiTranscript.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI said:")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.green)

                        Text("\"\(apiManager.aiTranscript)\"")
                            .italic()
                            .padding()
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(8)
                    }
                }

                Spacer(minLength: 50)
            }
            .padding()
        }
        .onAppear {
            // Connect managers
            audioManager.apiManager = apiManager
            audioManager.startContinuousRecording()
        }
        .onDisappear {
            audioManager.cleanup()
            webSocketManager.disconnect()
        }
    }
}

#Preview {
    ContentView()
}
