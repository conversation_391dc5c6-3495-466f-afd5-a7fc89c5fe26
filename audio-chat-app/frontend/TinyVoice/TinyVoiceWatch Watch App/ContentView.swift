//
//  ContentView.swift
//  TinyVoiceWatch Watch App
//
//  Created by <PERSON> on 8/14/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var audioManager = WatchAudioManager()
    @StateObject private var apiManager = WatchAPIManager()

    var body: some View {
        VStack(spacing: 16) {
            // Status indicator
            VStack(spacing: 8) {
                Image(systemName: audioManager.isRecording ? "mic.fill" :
                      audioManager.isProcessing ? "gear" : "mic")
                    .font(.title2)
                    .foregroundColor(audioManager.isRecording ? .red :
                                   audioManager.isProcessing ? .orange : .blue)

                Text(audioManager.status)
                    .font(.caption)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }

            // Main record button
            Button(action: {
                if audioManager.isRecording {
                    audioManager.stopRecording()
                } else {
                    audioManager.startRecording()
                }
            }) {
                Circle()
                    .fill(audioManager.isRecording ? Color.red : Color.blue)
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: audioManager.isRecording ? "stop.fill" : "mic.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                    )
            }
            .disabled(audioManager.isProcessing)
            .buttonStyle(PlainButtonStyle())

            // Error message
            if !apiManager.errorMessage.isEmpty {
                Text(apiManager.errorMessage)
                    .font(.caption2)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .padding()
        .onAppear {
            audioManager.apiManager = apiManager
            audioManager.setupAudioSession()
        }
        .onDisappear {
            audioManager.cleanup()
        }
    }
}

#Preview {
    ContentView()
}
