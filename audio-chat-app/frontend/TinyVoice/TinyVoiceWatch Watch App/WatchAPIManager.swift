//
//  WatchAPIManager.swift
//  TinyVoiceWatch Watch App
//
//  Created by <PERSON> on 8/16/25.
//

import Foundation
import Combine

class WatchAPIManager: ObservableObject {
    @Published var isProcessing = false
    @Published var errorMessage = ""
    
    // Use the same API endpoint as the iOS app
    private let apiURL = URL(string: "http://*************:8000/api/chat")!
    private var cancellables = Set<AnyCancellable>()
    
    struct APIResponse: Codable {
        let audio_data: String
        let transcript: String
        let user_transcript: String
    }
    
    func sendAudio(_ audioData: Data, completion: @escaping (Result<APIResponse, Error>) -> Void) {
        isProcessing = true
        errorMessage = ""
        
        // Create multipart form data
        let boundary = UUID().uuidString
        var request = URLRequest(url: apiURL)
        request.httpMethod = "POST"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0 // 30 second timeout for watchOS
        
        var body = Data()
        
        // Add audio file
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"audio_file\"; filename=\"recording.wav\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: audio/wav\r\n\r\n".data(using: .utf8)!)
        body.append(audioData)
        body.append("\r\n--\(boundary)--\r\n".data(using: .utf8)!)
        
        request.httpBody = body
        
        URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isProcessing = false
            }
            
            if let error = error {
                DispatchQueue.main.async {
                    self?.errorMessage = "Network error: \(error.localizedDescription)"
                }
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                let error = NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
                DispatchQueue.main.async {
                    self?.errorMessage = "Invalid response from server"
                }
                completion(.failure(error))
                return
            }
            
            guard httpResponse.statusCode == 200 else {
                let error = NSError(domain: "APIError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server error: \(httpResponse.statusCode)"])
                DispatchQueue.main.async {
                    self?.errorMessage = "Server error: \(httpResponse.statusCode)"
                }
                completion(.failure(error))
                return
            }
            
            guard let data = data else {
                let error = NSError(domain: "APIError", code: 0, userInfo: [NSLocalizedDescriptionKey: "No data received"])
                DispatchQueue.main.async {
                    self?.errorMessage = "No data received from server"
                }
                completion(.failure(error))
                return
            }
            
            do {
                let apiResponse = try JSONDecoder().decode(APIResponse.self, from: data)
                DispatchQueue.main.async {
                    self?.errorMessage = "" // Clear any previous errors
                }
                completion(.success(apiResponse))
            } catch {
                DispatchQueue.main.async {
                    self?.errorMessage = "Failed to parse response: \(error.localizedDescription)"
                }
                completion(.failure(error))
            }
        }.resume()
    }
}
